from sqlalchemy import create_engine
import pandas as pd
from config import *
from utils import upload_data_multithread
from email_processor import process_emails
from phone_processor import process_phones
import math
from concurrent.futures import ProcessPoolExecutor
from concurrent.futures import as_completed
import time
import logging
import argparse

# 这里设置日志
logger = logging.getLogger('Doris_Upload_Logger')
logger.setLevel(logging.INFO)
# 设置日志格式
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler = logging.FileHandler('doris_upload.log')
file_handler.setFormatter(formatter)
logger.addHandler(file_handler)


def process_email_data():
    start_time = time.time()
    logger.info("邮箱数据处理开始")

    engine = create_engine(f'mysql+pymysql://{DORIS_USER}:{DORIS_PASSWORD}@{DORIS_HOST}:{DORIS_PORT}/{DORIS_DB}')
    conn = engine.connect()

    df_max = pd.read_sql("""SELECT max(id) FROM ic_relation.{}""".format(READ_TABLE_EMAIL), conn)

    max_id = df_max.iloc[0, 0]
    N = math.ceil(max_id / BATCH_SIZE)

    total_processed_records = 0
    for i in range(N):
        logger.info("this is epoch %s / %s" % (i, N))

        batch_start_time = time.time()

        sql = """
        SELECT company_id, company_email
        FROM ic_relation.{}
        where {} <= id and id <= {} 
        """.format(READ_TABLE_EMAIL, BATCH_SIZE*(i)+1, BATCH_SIZE*(i+1))

        # 执行查询，获取数据
        df = pd.read_sql(sql, conn)

        # # 如果本次查询没有数据，跳出循环
        if df.empty:
            continue

        cpu_num = CPU_NUM

        # 计算每份的大小
        chunk_size = len(df) // cpu_num  # 每份的大小
        remainder = len(df) % cpu_num  # 余数
        
        # 确定每个 cpu 核心要处理的数据
        var = []
        start = 0   
        for i in range(cpu_num):
            # 如果有余数，前remainder份每份多分配一条数据
            end = start + chunk_size + (1 if i < remainder else 0)
            var.append(df.iloc[start:end])
            start = end

        result = []

        with ProcessPoolExecutor(max_workers=cpu_num) as executor:
            futures = []
            for i in var:
                # submit 方法会立即返回一个 Future 对象，该对象代表异步执行的操作
                futures.append(executor.submit(process_emails, i))
            for res in futures:
                # 调用 res.result() 方法来获取异步执行的结果，并将其添加到 result 列表中
                result.append(res.result())

        # 利用result拼接df
        valid_data = pd.DataFrame()

        for i in result:
            valid_data = pd.concat([valid_data,i],axis=0,ignore_index=True) 

        # if not valid_data.empty:
        #     upload_data_multithread(valid_data, thread_sep_num=THREAD_SEP_NUM, doris_port=DORIS_PORT_IMPORT, table=UPLOAD_TABLE_EMAIL, batchsize=UPLOAD_BATCH_SIZE, logger=logger)

        # 计算并输出每批次处理时间
        batch_time = time.time() - batch_start_time
        total_processed_records += len(df)

        # print(f"邮箱：已处理 {len(df)} 条记录... 本批次耗时: {batch_time:.2f}秒")
        # print(f"邮箱：平均处理速度: {len(df)/batch_time:.2f} 条/秒")
        # print(f"邮箱：已处理 {total_processed_records} 条记录...")

        logger.info(f"邮箱：已处理 {len(df)} 条记录... 本批次耗时: {batch_time:.2f}秒")
        logger.info(f"邮箱：平均处理速度: {len(df)/batch_time:.2f} 条/秒")
        logger.info(f"邮箱：已处理 {total_processed_records} 条记录...")

    process_time = time.time() - start_time
    logger.info(f"邮箱数据处理完成，总耗时: {process_time:.2f}秒")

    conn.close()
    # print("邮箱数据处理完成！")


def process_phone_data():
    """
    将电话数据分批，多进程处理，最后上传
    """
    start_time = time.time()
    logger.info("电话数据处理开始")

    engine = create_engine(f'mysql+pymysql://{DORIS_USER}:{DORIS_PASSWORD}@{DORIS_HOST}:{DORIS_PORT}/{DORIS_DB}')
    conn = engine.connect()

    df_max = pd.read_sql("""SELECT max(id) FROM ic_relation.{}""".format(READ_TABLE_PHONE), conn)

    max_id = df_max.iloc[0, 0]
    N = math.ceil(max_id / BATCH_SIZE)

    total_processed_records = 0
    total_valid_records = 0  # 新增变量，累计有效数据的总记录数

    # max_epoch = 5

    for i in range(N):
        logger.info("========== this is epoch %s / %s ==========" % (i, N) )
        batch_start_time = time.time()

        # if i >= max_epoch:
        #     logger.info("已经处理了 %s 个epoch，退出" % (max_epoch))
        #     break   

        sql = """
        SELECT company_id, company_phone
        FROM ic_relation.{}
        where {} <= id and id <= {}
        """.format(READ_TABLE_PHONE, BATCH_SIZE*(i)+1, BATCH_SIZE*(i+1))
        # [1, BATCH_SIZE], [BATCH_SIZE+1, BATCH_SIZE*2], [BATCH_SIZE*2+1, BATCH_SIZE*3], ...

        # # 执行查询，获取数据
        df = pd.read_sql(sql, conn)
        
        # print(df.head())

        # 如果本次查询没有数据，跳出循环
        if df.empty:
            continue

        cpu_num = CPU_NUM  # cpu核心数

        # 计算每份的大小
        chunk_size = len(df) // cpu_num
        remainder = len(df) % cpu_num
        
        var = []
        start = 0   
        for i in range(cpu_num):
            # 如果有余数，前remainder份中每份多分配一条数据
            # [start, start+chunk_size+1), [start+chunk_size+1, start+2*chunk_size+2), ..., [start+?*chunk_size+reminder, start+(?+1)*chunk_size+reminder)
            end = start + chunk_size + (1 if i < remainder else 0)
            var.append(df.iloc[start:end])
            start = end

        result = []

        with ProcessPoolExecutor(max_workers=cpu_num) as executor:
            futures = []
            for i in var:
                futures.append(executor.submit(process_phones, i))
            # for res in futures:
            #     result.append(res.result())
            # 等待所有任务完成
            for res in as_completed(futures):
                try:
                    result.append(res.result())
                except Exception as e:
                    logger.error(f"Error occurred: {e}")


        # 接下来利用result拼接df
        valid_data = pd.DataFrame()
        for i in result:
            valid_data = pd.concat([valid_data,i],axis=0,ignore_index=True) 


        total_valid_records += len(valid_data)  # 累计有效数据的总记录数
        # print(valid_data.drop_duplicates().to_string(index=False))

        if not valid_data.empty:
            upload_data_multithread(valid_data, thread_sep_num=THREAD_SEP_NUM, doris_port=DORIS_PORT_IMPORT, table=UPLOAD_TABLE_PHONE, batchsize=UPLOAD_BATCH_SIZE, logger=logger)

        # 计算并输出每批次处理时间
        batch_time = time.time() - batch_start_time
        total_processed_records += len(df)

        logger.info(f"本批次：得到有效数据 {len(valid_data)} / 处理原始数据 {len(df)} 条")
        logger.info(f"本批次：处理+上传耗时: {batch_time:.2f}秒  速度: {len(df)/batch_time:.2f} 条/秒")    

    logger.info(f"共得到有效数据 {total_valid_records} / 共处理原始数据 {total_processed_records} 条")
    
    process_time = time.time() - start_time
    logger.info(f"电话数据处理完成，总耗时: {process_time:.2f}秒")
    conn.close()


if __name__ == "__main__":
    logger.info("=== 数据处理任务开始 ===")
    start_time = time.time()

    parser = argparse.ArgumentParser(description="数据处理任务")
    parser.add_argument("--email", action="store_true", help="处理邮件数据")
    parser.add_argument("--phone", action="store_true", help="处理电话数据")
    args = parser.parse_args()

    try:
        print("All log information will be saved in doris_upload.log file.")
        print("Code is running ......")
        if args.email:
            process_email_data()
        elif args.phone:
            process_phone_data()
        else:
            logger.info("没有指定处理任务，不会执行任何任务。")

        total_time = time.time() - start_time
        logger.info(f"=== 所有数据处理完成，总耗时: {total_time:.2f}秒 ===")
    
    except Exception as e:
        logger.error(f"程序执行异常: {str(e)}", exc_info=True)
        raise