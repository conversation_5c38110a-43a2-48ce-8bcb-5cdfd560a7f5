import pandas as pd
import re
from emval import validate_email, EmailValidator

# 定义正则表达式
delimiter_regex = re.compile(r'[\\/、;；，,]+')  # 匹配 \ / 、 ; ； ， , 等分隔符
SPECIAL_CHARS_REGEX = re.compile(r'[\s\\\[\]{}|`\'"<>,;:!?#$%&*=/\u0000-\u001F\u007F-\uFFFF]')
CONSECUTIVE_DIGITS_REGEX = re.compile(r'(\d)\1{2,}')  # 匹配连续三个或更多相同数字


# 验证邮箱
def clean_and_validate_email(email):
    try:
        # 移除中文字符和不允许的特殊字符
        cleaned_email = SPECIAL_CHARS_REGEX.sub('', ''.join(re.findall(r'[^\u4e00-\u9fff]', email)))
        
        # 拆分邮箱为 local_part 和 domain_part
        local_part, domain_part = cleaned_email.split('@', 1)
        
        # 如果是 QQ 邮箱，则进行特定验证
        if domain_part.lower() == 'qq.com':
            if local_part.isdigit():
                # 验证全数字的 QQ 邮箱，长度限制为 5-11 位
                if not re.fullmatch(r'[1-9]\d{4,10}', local_part):
                    return False
            else:
                # 验证包含字母的 QQ 邮箱，不限制长度，只允许字母和数字
                if not re.fullmatch(r'[A-Za-z0-9]+', local_part):
                    return False
        
        # 检查 local_part 中的字符是否全部相同
        if len(set(local_part)) == 1:
            return False
        
        # 检查 local_part 是否包含两个以上的连续三个或更多的数字序列
        consecutive_sequences = re.findall(r'(?:(123|234|345|456|567|678|789|890|901))', local_part)
        if len(consecutive_sequences) >= 2:
            return False

        # 检查 local_part 是否包含连续三个或更多相同数字，111
        if CONSECUTIVE_DIGITS_REGEX.search(local_part):
            return False

        val_email = validate_email(cleaned_email)
        return val_email.normalized  # 返回规范化的邮箱地址

    except Exception as e:  # 处理不合法的邮箱和其他错误
        # print(str(e))
        return False
    

def process_emails(df):
    """
    处理DataFrame中的邮箱数据
    Args:
        df: 包含邮箱数据的DataFrame[company_id, company_email]
    Returns:
        DataFrame: 只包含有效邮箱的数据[company_id, company_email]
    """

    
    # 根据正则delimiter_regex分割邮箱，将一个邮箱字符串拆分为多个邮箱字符串
    # 创建新列company_email_，其值为分割后的邮箱列表，然后展开为多行
    # str.strip() 用于去除邮箱字符串两端的空白字符
    df_exploded = df.assign(
        company_email_=df['company_email'].str.strip().str.split(delimiter_regex)
    ).explode('company_email_')


    # 使用clean_and_validate_email进行初步过滤
    df_exploded['email_check'] = df_exploded['company_email_'].apply(clean_and_validate_email)


    # 添加过滤条件，只保留非False和非0的值
    valid_emails = df_exploded[df_exploded['email_check'].astype(bool)][['company_id', 'email_check']].rename(
        columns={'email_check': 'company_email'}
    )

    return valid_emails