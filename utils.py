import logging
import requests
import json
import base64
import math
import numpy as np
from concurrent.futures import ThreadPoolExecutor, as_completed
from config import *
import uuid  


# Doris数据导入函数
def import_data(data_0, doris_port_import, table, batchsize, logger_name):
    '''
    <PERSON>数据导入函数
    ----------------
    data_0: 需要导入的数据，DataFrame 格式
    doris_port_import: 导入数据的端口
    table: 需要导入数据的表
    batchsize: 一次导入多少条数据,即分割数据的规模
    logger_name: 日志对象，处理异常
    其他的Doris配置参数都一样,但是账号密码要在外面用base处理
    '''
    total_df = 0
    sep_data_num = math.ceil(len(data_0) / batchsize)  # 分割的份数
    # 将数据分割
    df_list = [data_0.iloc[batchsize * (i - 1):batchsize * i, :] 
               if i < sep_data_num
               else data_0.iloc[batchsize * (i - 1):len(data_0), :]
               for i in range(1, sep_data_num + 1)]
    # print('utils.py import_data()')
    # print(df_list)
    # Stream Load 的 URL
    url = f'http://{DORIS_HOST}:{doris_port_import}/api/{DATABASE}/{table}/_stream_load'
    # 上传数据到第二张表
    url2 = f'http://{DORIS_HOST}:{doris_port_import}/api/{DATABASE}/rule5_company_phone_o8/_stream_load'
    
    # 设置 Headers
    random_uuid = uuid.uuid4().hex
    random_uuid2 = uuid.uuid4().hex

    headers = {
        'label': random_uuid,
        'format': 'json',
        'strip_outer_array': 'true',
        'Expect': '100-continue',
        'Authorization': f'Basic ' + str(STR_P)[2:-1]
    }

    headers2 = {
        'label': random_uuid2,
        'format': 'json',
        'strip_outer_array': 'true',
        'Expect': '100-continue',
        'Authorization': f'Basic ' + str(STR_P)[2:-1]
    }

    # 开始循环插数
    for df_i in df_list:
        df_i = df_i.replace(np.nan, None)  # 替换np.nan为None, doris中只能接受None形式的空缺值
        df_i = df_i.to_dict('records')
        for v in df_i:
            v['uid'] = random_uuid
        json_data = json.dumps(df_i)
        
        # print(json_data)
        
        # 发送 HTTP 请求
        response = requests.put(url, headers=headers, data=json_data)
        response2 = requests.put(url2, headers=headers2, data=json_data)

        # 检查响应
        if response.status_code != 200 or response2.status_code != 200:  
            # 如果第一个 response 异常，记录其信息  
            if response.status_code != 200:  
                logger_name.error(f'Failed to import data to first table ({table}). Status code: {response.status_code}')  
                logger_name.error(f'Response: {response.text}, UUID: {random_uuid}')  
            # 如果第二个 response 异常，记录其信息  
            if response2.status_code != 200:  
                logger_name.error(f'Failed to import data to second table (rule5_company_phone_o8). Status code: {response2.status_code}')  
                logger_name.error(f'Response: {response2.text}, UUID: {random_uuid2}')  
            logger_name.error(f'Failed batch size: {len(df_i)} records.')  
        else:  
            # 如果都正常，更新计数和记录日志  
            total_df += len(df_i)  
            logger_name.info(f'Successfully imported {random_uuid} - {random_uuid2} - {len(df_i)} ')
   
    return total_df


# 多线程上传函数
def upload_data_multithread(data, thread_sep_num, doris_port, table, batchsize, logger):
    '''
    多线程上传数据函数
    ---------------
    data: 需要上传的DataFrame数据
    thread_sep_num: 定义线程数
    doris_port: Doris的端口
    table: 目标表
    batchsize: 每批上传的规模
    logger: 日志对象
    '''
    thread_batch = math.ceil(len(data) / thread_sep_num)  # 一个线程负责的batch规模
    thread_list = []  # 线程数据列表

    total_imported_records = 0

    # 将数据划分为每个线程处理的部分
    for th in range(1, thread_sep_num + 1):
        if th < thread_sep_num:
            # 如果不是最后一个线程，分配 thread_batch 大小的数据
            thread_list.append(data.iloc[thread_batch * (th - 1):thread_batch * th, :])
        else:
            # 如果是最后一个线程，分配剩余的数据
            thread_list.append(data.iloc[thread_batch * (th - 1):len(data), :])

    # 创建线程池并执行任务
    # 使用 ThreadPoolExecutor 创建线程池，最大线程数为 thread_sep_num。
    with ThreadPoolExecutor(max_workers=thread_sep_num) as t:
        obj_list = []  # 任务列表
        for task in range(thread_sep_num):
            # 提交任务到线程池，调用 import_data 函数
            obj = t.submit(import_data, thread_list[task], doris_port, table, batchsize, logger)
            obj_list.append(obj)

        # 等待所有任务完成
        for future in as_completed(obj_list):
            obj_result = future.result()
            total_imported_records += obj_result
    
    logger.info(f"本批次上传完成，上传数据总条数为 {total_imported_records}")