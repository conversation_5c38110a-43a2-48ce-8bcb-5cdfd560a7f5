import pandas as pd
import re
from sqlalchemy import create_engine


delimiter_regex = re.compile(r'[\\/、;；，,]+')  # 匹配 \ / 、 ; ； 等分隔符  增加，,
non_digit_regex = re.compile(r'[^\d]')  # 仅保留数字

# 定义正则表达式
# phone_regex = re.compile(
#     r'^(?:\+?86)?1(?:3\d{3}|5[^4\D]\d{2}|8\d{3}|7(?:[235-8]\d{2}|4(?:0\d|1[0-2]|9\d))|'
#     r'9[0-35-9]\d{2}|66\d{2})\d{6}$'
# )
# 版本2
phone_regex = re.compile(r'^(?:\+?86)?1(3[0-9]|4[5-9]|5[0-3,5-9]|6[5-6]|7[0-8]|8[0-9]|9[1,8-9])\d{8}$')

# landline_regex = re.compile(
#     r'^(?:\+?86)?0(?:(?:(?:(?:2[^6])|3(?:1[^1]|49|5\d|7[^1789]|9[^079])|'
#     r'4(?:1[^0134]|2[179]|3[^0-2]|5[^01]|6[4789]|7\d|8[23])|5(?:3[^12]|4[36]|'
#     r'5[^1]|6[12346]|7[280]|80|9[^015])|6(?:3[1235]|6[0238]|9[12])|7(?:01|(?:1|7)\d|'
#     r'2[248]|3[^123]|4[3-6]|5[^457]|6[02368]|9[^1])|8(?:1[23678]|2[567]|3\d|5[4-9]|'
#     r'7[^1]|8[3678]|9[1-7])|9(?:1[^08]|(?:3|7|9)\d|4[13]|5[1-5]|0[^0457]))-?\d{7})|'
#     r'(?:3(?:11|7[179])|4(?:[135]1|32)|5(?:1\d|2[37]|3[12]|51|7[^028]|9[15])|'
#     r'7(?:31|5[457]|69|91)|8(?:[57]1|98))-?\d{8})$'
# )
# 查看中间分隔符除了「-」和空白符还有什么？
landline_regex = re.compile(
    r'^0?((10|21|22|23|24|25|27|28|29|30|311|371|377|379|411|431|432|451|51[0-9]|523|527|531|532|551|57[1,3-7,9]|59[1,5]|731|75[1,4-5,7]|76[0,9]|754|755|757|760|769|791|851|871|898)[-\s]*\d{8}|(31[0,2-9]|335|349|35[0-9]|37[0,2-7]|39[1-6,8]|41[0,2-9]|42[1,7,9]|43[3-9]|440|448|45[0-9]|46[4,7-9]|47[0-9]|48[2-3]|520|53[0,3-9]|54[3,6]|63[1-5]|55[0,2-9]|56[1-4]|57[0,2,8]|580|59[2-4,6-9]|66[0-3,8]|75[0-3,6,8,9]|76[2-3,5-6,8]|69[1-2]|87[0,2-9]|88[1,3,6-8]|71[0-9]|72[2,4,8]|73[0,2-9]|74[3-6]|77[0-9]|701|79[0,2-9]|81[0-3,6-9]|82[5-7]|83[0-9]|85[2-9]|89[1-7]|89[0,9]|91[0-7,9]|93[0-9]|94[1,3]|95[1-5]|97[0-9]|99[0-9]|90[1-3,6,8,9])[-\s]*\d{7}|(814|815)[-\s]*\d{6})$'
)

# 检查电话号码是否由相同字符组成
def is_repeated_chars(phone: str) -> bool:
    remaining = phone[3:]
    return len(set(remaining)) == 1  # 检查前三位后的所有字符是否相同

# 匹配4个及以上的连续递增数字序列
CONSECUTIVE_SEQUENCE_REGEX = re.compile(r'(?:0123|1234|2345|3456|4567|5678|6789|7890|8901|9012)')

def has_repeated_sequence(phone: str) -> bool:
    """
    检查电话号码是否包含不允许的重复序列。
    
    条件：
    1. 长度大于等于2的序列连续出现两次及以上。
    2. 单个数字连续出现5次及以上。
    3. 有两个单个数字都连续出现4次。
    4. 连续三个或更多相同数字。
    
    参数:
    phone (str): 待检查的电话号码字符串（仅包含数字）。
    
    返回:
    bool: 如果包含不允许的重复序列返回True，否则返回False。
    """            
    # 1. 检查长度大于等于2的序列连续出现三次及以上
    for length in range(2, len(phone) // 3 + 1):
        for i in range(len(phone) - length * 3 + 1):
            sequence = phone[i:i+length]
            if phone[i:i+length*3] == sequence * 3:
                return True
    
    # 2. 检查长度大于等于3的序列连续出现两次及以上
    for length in range(3, len(phone) // 2 + 1):
        for i in range(len(phone) - length * 2 + 1):
            sequence = phone[i:i+length]
            if phone[i:i+length*2] == sequence * 2:
                return True
            
    # 2. 检查单个数字是否连续出现5次及以上
    if any(digit * 5 in phone for digit in set(phone)):
        return True

    # 3. 检查是否有两个单个数字都连续出现4次
    four_repeats = [digit for digit in set(phone) if digit * 4 in phone]
    if len(four_repeats) >= 2:
        return True

    # 4. 检查是否包含4个及以上的连续递增数字序列
    if CONSECUTIVE_SEQUENCE_REGEX.search(phone):
        return True
    return False

def clean_and_validate_phone(phone: str):
    """
    清理并验证电话号码，返回有效的电话号码列表。
    
    参数:
    phone (str): 待验证的电话号码字符串。
    
    返回:
    phone (str): 有效的电话号码
    """

    if pd.isnull(phone):
        return []
    
    # 提取仅数字
    cleaned_phone = non_digit_regex.sub('', phone)
    
    # 原有的验证逻辑
    if is_repeated_chars(cleaned_phone):
        return False
    
    if has_repeated_sequence(cleaned_phone):
        return False
    
    # 检查是否可能包含区号+手机号的情况  如: 010-13651016295  北京奥绿苑生物资源技术开发有限公司
    if len(cleaned_phone) > 11:
        possible_mobile = cleaned_phone[-11:]
        if phone_regex.match(possible_mobile):
            return possible_mobile
    
    if phone_regex.match(cleaned_phone) or landline_regex.match(cleaned_phone):
        return cleaned_phone
    
    return False


def process_phones(df):
    """
    处理DataFrame中的电话数据
    Args:
        df: 包含电话数据的DataFrame[company_id, company_phone]
    Returns:
        DataFrame: 只包含有效电话的数据[company_id, company_phone]
    """
    
    # 根据正则delimiter_regex分割电话，将一个电话字符串拆分为多个电话字符串
    # 创建新列company_phone_，其值为分割后的电话列表，然后展开为多行
    # str.strip() 用于去除电话字符串两端的空白字符
    df_exploded = df.assign(
        company_phone_=df['company_phone'].str.strip().str.split(delimiter_regex)
    ).explode('company_phone_')

    # 使用clean_and_validate_email进行初步过滤
    df_exploded['phone_check'] = df_exploded['company_phone_'].apply(clean_and_validate_phone)

    # 添加过滤条件，只保留非False和非0的值
    valid_phones = df_exploded[df_exploded['phone_check'].astype(bool)][['company_id', 'phone_check']].rename(
        columns={'phone_check': 'company_phone'}
    )

    return valid_phones